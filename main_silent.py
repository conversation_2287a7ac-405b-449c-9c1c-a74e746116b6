import sys
import os
import gc
import traceback
import warnings

# إخفاء جميع التحذيرات والرسائل التقنية
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['QT_LOGGING_RULES'] = '*=false'

# فئة لتصفية الرسائل التقنية
class TechnicalMessageFilter:
    def __init__(self, original_stream):
        self.original_stream = original_stream

    def write(self, message):
        # قائمة شاملة للرسائل التقنية المراد إخفاؤها
        blocked_patterns = [
            'Unknown property',
            'text-shadow',
            'box-shadow',
            'transform',
            'transition',
            'backdrop-filter',
            'does not have a property named',
            'alignment',
            'qproperty-alignment',
            'overflow',
            'text-overflow',
            'QPainter::',
            'QWidgetEffectSourcePrivate::',
            'Painter not active',
            'paint device can only be painted',
            'worldTransform',
            'setWorldTransform',
            'translate',
            'arguments did not match'
        ]

        # إذا كانت الرسالة لا تحتوي على أي من الأنماط المحظورة، اعرضها
        if not any(pattern in message for pattern in blocked_patterns):
            self.original_stream.write(message)

    def flush(self):
        self.original_stream.flush()

# تطبيق مرشح الرسائل
sys.stderr = TechnicalMessageFilter(sys.__stderr__)

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt, QTimer, qInstallMessageHandler
from PyQt5.QtGui import QFont

from ui.main_window import MainWindow
from database import init_db, get_session, User
from performance_optimizer import apply_performance_optimizations, setup_performance_monitoring
from utils import check_due_invoices, check_due_supplier_payments, check_upcoming_payments

def setup_application():
    """إعداد تطبيق PyQt مع إخفاء الرسائل التقنية"""

    # تعطيل رسائل Qt التقنية
    def qt_message_handler(mode, context, message):
        # قائمة الرسائل المسموح بها (الأخطاء المهمة فقط)
        important_patterns = [
            'Error',
            'Critical',
            'Fatal',
            'Exception'
        ]

        # السماح فقط بالرسائل المهمة
        if any(pattern in message for pattern in important_patterns):
            print(f"Qt Message: {message}")

    qInstallMessageHandler(qt_message_handler)

    # تعيين خيارات الأداء العالي قبل إنشاء التطبيق
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    QApplication.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)

    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)

    # تعيين اسم التطبيق
    app.setApplicationName("برنامج المحاسبة الإداري")

    # تعيين نمط التطبيق
    app.setStyle("Fusion")

    # تعيين اتجاه النص من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين الخط الافتراضي
    try:
        font = QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        app.setFont(font)
    except:
        pass  # تجاهل أخطاء الخط

    return app

def safe_refresh_current_tab(window):
    """تحديث التبويب الحالي بشكل آمن"""
    try:
        current_tab = window.tabs.currentWidget()
        if current_tab and hasattr(current_tab, 'refresh_data') and callable(current_tab.refresh_data):
            try:
                current_tab.refresh_data()
            except Exception as e:
                print(f"خطأ في تحديث بيانات التبويب الحالي: {str(e)}")
    except Exception as e:
        print(f"خطأ في الوصول إلى التبويب الحالي: {str(e)}")

def check_payments(session, window):
    """التحقق من المدفوعات المستحقة والقادمة وإنشاء تنبيهات"""
    try:
        # التحقق من الفواتير المستحقة
        due_invoices = check_due_invoices(session)
        if due_invoices:
            print(f"تم العثور على {len(due_invoices)} فاتورة مستحقة")

        # التحقق من المدفوعات المستحقة للموردين
        due_expenses = check_due_supplier_payments(session)
        if due_expenses:
            print(f"تم العثور على {len(due_expenses)} دفعة مستحقة للموردين")

        # التحقق من المدفوعات القادمة
        upcoming_invoices, upcoming_expenses = check_upcoming_payments(session)
        if upcoming_invoices:
            print(f"تم العثور على {len(upcoming_invoices)} فاتورة قادمة خلال أسبوع")
        if upcoming_expenses:
            print(f"تم العثور على {len(upcoming_expenses)} دفعة قادمة للموردين خلال أسبوع")

        # تحديث عدد الإشعارات في النافذة الرئيسية
        if hasattr(window, 'update_notification_count'):
            window.update_notification_count()

    except Exception as e:
        print(f"خطأ في التحقق من المدفوعات المستحقة: {str(e)}")

def main():
    """الدالة الرئيسية للتطبيق"""
    try:
        print("🚀 تشغيل البرنامج المحاسبي...")
        print("🔇 تم تفعيل مرشح الرسائل التقنية")

        # تطبيق تحسينات الأداء قبل بدء البرنامج
        apply_performance_optimizations()

        # إعداد تطبيق PyQt
        app = setup_application()

        # تعطيل تحديث الشاشة المستمر لتحسين الأداء
        app.setEffectEnabled(Qt.UI_AnimateCombo, False)
        app.setEffectEnabled(Qt.UI_AnimateTooltip, False)
        app.setEffectEnabled(Qt.UI_FadeMenu, False)
        app.setEffectEnabled(Qt.UI_FadeTooltip, False)
        app.setEffectEnabled(Qt.UI_AnimateMenu, False)

        # إعداد قاعدة البيانات
        init_db()

        # إنشاء جلسة قاعدة البيانات
        session = get_session()

        # إنشاء أو الحصول على المستخدم الإداري الافتراضي
        user = session.query(User).filter_by(role="admin").first()
        if not user:
            # إنشاء مستخدم إداري افتراضي
            user = User(
                username="admin",
                password="admin",                  role="admin",
                full_name="المدير العام"
            )
            session.add(user)
            session.commit()

        print("✅ تم إعداد قاعدة البيانات والمستخدم")

        # إنشاء النافذة الرئيسية مباشرة
        window = MainWindow(session, user)

        # إعداد مراقبة الأداء للنافذة الرئيسية
        setup_performance_monitoring(window)

        # تنظيف الذاكرة قبل عرض النافذة
        gc.collect()

        # إظهار النافذة الرئيسية
        window.showMaximized()
        print("✅ تم تشغيل النافذة الرئيسية")

        # معالجة الأحداث المعلقة بعد عرض النافذة الرئيسية
        app.processEvents()

        # تأخير تحديث البيانات لتحسين وقت بدء التشغيل
        QTimer.singleShot(1000, lambda: safe_refresh_current_tab(window))

        # التحقق من المدفوعات المستحقة والقادمة
        QTimer.singleShot(2000, lambda: check_payments(session, window))

        print("")
        print("🎉 تم تشغيل البرنامج بنجاح!")
        print("🔇 الرسائل التقنية مخفية")
        print("🔐 الدخول: admin/admin")
        print("")

        # تشغيل حلقة الأحداث
        sys.exit(app.exec_())
    except Exception as e:
        # معالجة الأخطاء غير المتوقعة
        error_message = f"حدث خطأ غير متوقع: {str(e)}\n\n{traceback.format_exc()}"

        # محاولة عرض رسالة الخطأ في نافذة منبثقة
        try:
            QMessageBox.critical(None, "خطأ", error_message)
        except:
            # إذا فشل عرض النافذة المنبثقة، اطبع الخطأ في وحدة التحكم
            print(error_message)

        sys.exit(1)

if __name__ == "__main__":
    main()
