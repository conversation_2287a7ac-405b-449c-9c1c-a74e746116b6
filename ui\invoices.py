from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QFileDialog, QMenu, QAction, QSizePolicy, QFrame)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import Invoice, InvoiceItem, Client
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency,
                    generate_invoice_number, format_quantity)
import datetime
import re

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel)

class InvoiceItemDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل عنصر فاتورة"""

    def __init__(self, parent=None, item=None):
        super().__init__(parent)
        self.item = item
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار
        if self.item:
            self.setWindowTitle("تعديل عنصر الفاتورة")
        else:
            self.setWindowTitle("إضافة عنصر جديد للفاتورة")

        self.setMinimumWidth(400)

        # إنشاء النموذج
        form_layout = QFormLayout()

        # حقل الوصف
        self.description_edit = QLineEdit()
        self.description_edit.setStyleSheet(UnifiedStyles.get_input_style())
        if self.item:
            self.description_edit.setText(self.item.description)
        form_layout.addRow("الوصف:", self.description_edit)

        # حقل الكمية
        self.quantity_edit = QDoubleSpinBox()
        self.quantity_edit.setRange(1, 10000)  # الحد الأدنى 1 بدلاً من 0.01
        self.quantity_edit.setDecimals(0)  # بدون كسور عشرية
        self.quantity_edit.setSingleStep(1)
        self.quantity_edit.setValue(1)
        if self.item:
            self.quantity_edit.setValue(self.item.quantity)
        self.quantity_edit.valueChanged.connect(self.calculate_total)
        form_layout.addRow("الكمية:", self.quantity_edit)

        # حقل سعر الوحدة
        self.unit_price_edit = QDoubleSpinBox()
        self.unit_price_edit.setRange(1, 1000000)  # الحد الأدنى 1 بدلاً من 0.01
        self.unit_price_edit.setDecimals(0)  # بدون كسور عشرية
        self.unit_price_edit.setSingleStep(10)
        if self.item:
            self.unit_price_edit.setValue(self.item.unit_price)
        self.unit_price_edit.valueChanged.connect(self.calculate_total)
        form_layout.addRow("سعر الوحدة:", self.unit_price_edit)

        # حقل السعر الإجمالي
        self.total_price_label = QLabel("0.00")
        form_layout.addRow("السعر الإجمالي:", self.total_price_label)

        # حساب السعر الإجمالي الأولي
        self.calculate_total()

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        self.save_button = StyledButton("💾 حفظ", "success", "normal")
        self.save_button.clicked.connect(self.accept)

        self.cancel_button = StyledButton("❌ إلغاء", "secondary", "normal")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.save_button.button)
        button_layout.addWidget(self.cancel_button.button)

        # تجميع التخطيط النهائي
        main_layout = QVBoxLayout()
        main_layout.addLayout(form_layout)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def calculate_total(self):
        """حساب السعر الإجمالي للعنصر"""
        quantity = self.quantity_edit.value()
        unit_price = self.unit_price_edit.value()
        total_price = quantity * unit_price
        self.total_price_label.setText(format_currency(total_price))

    def get_data(self):
        """الحصول على بيانات عنصر الفاتورة من النموذج"""
        description = self.description_edit.text().strip()
        quantity = self.quantity_edit.value()
        unit_price = self.unit_price_edit.value()
        total_price = quantity * unit_price

        # التحقق من صحة البيانات
        if not description:
            show_error_message("خطأ", "يجب إدخال وصف العنصر")
            return None

        if quantity <= 0:
            show_error_message("خطأ", "يجب أن تكون الكمية أكبر من صفر")
            return None

        if unit_price <= 0:
            show_error_message("خطأ", "يجب أن يكون سعر الوحدة أكبر من صفر")
            return None

        return {
            'description': description,
            'quantity': quantity,
            'unit_price': unit_price,
            'total_price': total_price
        }

class InvoiceDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل فاتورة"""

    def __init__(self, parent=None, invoice=None, session=None):
        super().__init__(parent)
        self.invoice = invoice
        self.session = session
        self.items = []

        # إذا كانت هناك فاتورة موجودة، نسخ عناصرها
        if self.invoice and self.invoice.items:
            for item in self.invoice.items:
                self.items.append({
                    'id': item.id,
                    'description': item.description,
                    'quantity': item.quantity,
                    'unit_price': item.unit_price,
                    'total_price': item.total_price
                })

        self.init_ui()
        self.update_total()

    def init_ui(self):
        # إعداد نافذة الحوار
        if self.invoice:
            self.setWindowTitle("تعديل فاتورة")
        else:
            self.setWindowTitle("إنشاء فاتورة جديدة")

        self.setMinimumWidth(800)
        self.setMinimumHeight(600)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إنشاء نموذج معلومات الفاتورة
        form_group = StyledGroupBox("معلومات الفاتورة", "primary")
        form_layout = QFormLayout()

        # حقل رقم الفاتورة
        self.invoice_number_edit = QLineEdit()
        if self.invoice:
            self.invoice_number_edit.setText(self.invoice.invoice_number)
        else:
            # إنشاء رقم فاتورة جديد
            self.invoice_number_edit.setText(generate_invoice_number(self.session))
        form_layout.addRow("رقم الفاتورة:", self.invoice_number_edit)

        # حقل العميل
        self.client_combo = QComboBox()
        self.client_combo.addItem("-- اختر عميل --", None)

        # إضافة العملاء من قاعدة البيانات
        if self.session:
            clients = self.session.query(Client).all()
            for client in clients:
                self.client_combo.addItem(client.name, client.id)

        # تحديد العميل الحالي إذا كان موجودًا
        if self.invoice and self.invoice.client_id:
            index = self.client_combo.findData(self.invoice.client_id)
            if index >= 0:
                self.client_combo.setCurrentIndex(index)

        form_layout.addRow("العميل:", self.client_combo)

        # حقل تاريخ الفاتورة
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        if self.invoice and self.invoice.date:
            self.date_edit.setDate(datetime_to_qdate(self.invoice.date))
        form_layout.addRow("تاريخ الفاتورة:", self.date_edit)

        # حقل تاريخ الدفع القادم
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setCalendarPopup(True)
        due_date = QDate.currentDate().addDays(30)  # افتراضي: 30 يوم
        self.due_date_edit.setDate(due_date)
        if self.invoice and self.invoice.due_date:
            self.due_date_edit.setDate(datetime_to_qdate(self.invoice.due_date))
        form_layout.addRow("تاريخ الدفع القادم:", self.due_date_edit)

        # حقل الحالة
        self.status_combo = QComboBox()
        statuses = ["pending", "paid", "partially_paid", "cancelled"]
        status_labels = ["قيد الانتظار", "مدفوعة", "مدفوعة جزئيًا", "ملغاة"]

        for i, status in enumerate(statuses):
            self.status_combo.addItem(status_labels[i], status)

        if self.invoice and self.invoice.status:
            index = self.status_combo.findData(self.invoice.status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)

        form_layout.addRow("الحالة:", self.status_combo)

        # حقل المبلغ المدفوع
        self.paid_amount_edit = QDoubleSpinBox()
        self.paid_amount_edit.setRange(0, 1000000)
        self.paid_amount_edit.setDecimals(0)  # بدون كسور عشرية
        self.paid_amount_edit.setSingleStep(100)
        if self.invoice:
            self.paid_amount_edit.setValue(self.invoice.paid_amount)
        form_layout.addRow("المبلغ المدفوع:", self.paid_amount_edit)

        form_group.setLayout(form_layout)

        # إنشاء جدول عناصر الفاتورة
        items_group = StyledGroupBox("عناصر الفاتورة", "primary")
        items_layout = QVBoxLayout()

        styled_table = StyledTable()
        self.items_table = styled_table.table
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["الوصف", "الكمية", "سعر الوحدة", "السعر الإجمالي", ""])
        self.items_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.items_table.setSelectionMode(QTableWidget.SingleSelection)
        self.items_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # أزرار إدارة العناصر
        items_buttons_layout = QHBoxLayout()

        self.add_item_button = StyledButton("➕ إضافة", "success", "normal")
        self.add_item_button.clicked.connect(self.add_item)

        self.edit_item_button = StyledButton("✏️ تعديل", "primary", "normal")
        self.edit_item_button.clicked.connect(self.edit_item)

        self.delete_item_button = StyledButton("🗑️ حذف", "danger", "normal")
        self.delete_item_button.clicked.connect(self.delete_item)

        items_buttons_layout.addWidget(self.add_item_button.button)
        items_buttons_layout.addWidget(self.edit_item_button.button)
        items_buttons_layout.addWidget(self.delete_item_button.button)

        # ملخص الفاتورة
        summary_layout = QHBoxLayout()
        self.total_amount_label = QLabel("المبلغ الإجمالي: 0.00")
        self.total_amount_label.setFont(QFont("Arial", 12, QFont.Bold))
        summary_layout.addWidget(self.total_amount_label)

        # إضافة المكونات إلى تخطيط العناصر
        items_layout.addWidget(self.items_table)
        items_layout.addLayout(items_buttons_layout)
        items_layout.addLayout(summary_layout)

        items_group.setLayout(items_layout)

        # حقل الملاحظات
        notes_group = StyledGroupBox("ملاحظات", "primary")
        notes_layout = QVBoxLayout()

        self.notes_edit = QTextEdit()
        if self.invoice and self.invoice.notes:
            self.notes_edit.setText(self.invoice.notes)

        notes_layout.addWidget(self.notes_edit)
        notes_group.setLayout(notes_layout)

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        self.save_button = StyledButton("💾 حفظ", "success", "normal")
        self.save_button.clicked.connect(self.accept)

        self.cancel_button = StyledButton("❌ إلغاء", "secondary", "normal")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.save_button.button)
        button_layout.addWidget(self.cancel_button.button)

        # تجميع التخطيط النهائي
        main_layout.addWidget(form_group.group_box)
        main_layout.addWidget(items_group.group_box)
        main_layout.addWidget(notes_group.group_box)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

        # تحديث جدول العناصر
        self.update_items_table()

    def update_items_table(self):
        """تحديث جدول عناصر الفاتورة"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.items_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.items_table.setRowCount(0)

            # إضافة الصفوف
            for row, item in enumerate(self.items):
                self.items_table.insertRow(row)

                # التحقق من وجود المفاتيح المطلوبة
                description = item.get('description', '')
                quantity = item.get('quantity', 0)
                unit_price = item.get('unit_price', 0)
                total_price = item.get('total_price', 0)

                # إضافة العناصر إلى الجدول
                self.items_table.setItem(row, 0, QTableWidgetItem(description))
                self.items_table.setItem(row, 1, QTableWidgetItem(format_quantity(quantity)))
                self.items_table.setItem(row, 2, QTableWidgetItem(format_currency(unit_price)))
                self.items_table.setItem(row, 3, QTableWidgetItem(format_currency(total_price)))

                # إضافة زر حذف
                delete_button = StyledButton("🗑️ حذف", "danger", "normal")
                # استخدام row كمعامل ثابت في lambda function
                row_index = row  # تخزين قيمة row في متغير جديد
                delete_button.clicked.connect(lambda _, idx=row_index: self.delete_item_at_row(idx))
                self.items_table.setCellWidget(row, 4, delete_button.button)

            # إعادة تمكين تحديث الجدول
            self.items_table.setUpdatesEnabled(True)
        except Exception as e:
            # إعادة تمكين تحديث الجدول في حالة حدوث خطأ
            self.items_table.setUpdatesEnabled(True)
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث جدول العناصر: {str(e)}")

    def update_total(self):
        """تحديث المبلغ الإجمالي للفاتورة"""
        try:
            # حساب المبلغ الإجمالي بطريقة آمنة
            total = 0
            for item in self.items:
                # التحقق من وجود المفتاح
                if 'total_price' in item:
                    total += item['total_price']

            self.total_amount_label.setText(f"المبلغ الإجمالي: {format_currency(total)}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حساب المبلغ الإجمالي: {str(e)}")

    def add_item(self):
        """إضافة عنصر جديد للفاتورة"""
        try:
            dialog = InvoiceItemDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    self.items.append(data)
                    self.update_items_table()
                    self.update_total()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إضافة العنصر: {str(e)}")

    def edit_item(self):
        """تعديل عنصر في الفاتورة"""
        try:
            selected_row = self.items_table.currentRow()
            if selected_row < 0:
                show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
                return

            # التحقق من أن الصف موجود في القائمة
            if selected_row >= len(self.items):
                show_error_message("خطأ", "العنصر غير موجود")
                return

            # إنشاء كائن عنصر مؤقت للتعديل
            temp_item = type('obj', (object,), self.items[selected_row])

            dialog = InvoiceItemDialog(self, temp_item)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # الاحتفاظ بمعرف العنصر إذا كان موجودًا
                    if 'id' in self.items[selected_row]:
                        data['id'] = self.items[selected_row]['id']

                    self.items[selected_row] = data
                    self.update_items_table()
                    self.update_total()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تعديل العنصر: {str(e)}")

    def delete_item(self):
        """حذف عنصر من الفاتورة"""
        selected_row = self.items_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        self.delete_item_at_row(selected_row)

    def delete_item_at_row(self, row):
        """حذف عنصر من الفاتورة بناءً على الصف"""
        try:
            # التحقق من أن الصف موجود في القائمة
            if row < 0 or row >= len(self.items):
                show_error_message("خطأ", "العنصر غير موجود")
                return

            if show_confirmation_message("تأكيد الحذف", "هل أنت متأكد من حذف هذا العنصر؟"):
                del self.items[row]
                self.update_items_table()
                self.update_total()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حذف العنصر: {str(e)}")

    def get_data(self):
        """الحصول على بيانات الفاتورة من النموذج"""
        try:
            invoice_number = self.invoice_number_edit.text().strip()
            client_id = self.client_combo.currentData()
            date = qdate_to_datetime(self.date_edit.date())
            due_date = qdate_to_datetime(self.due_date_edit.date())
            status = self.status_combo.currentData()
            paid_amount = self.paid_amount_edit.value()
            notes = self.notes_edit.toPlainText().strip()

            # حساب المبلغ الإجمالي بطريقة آمنة
            total_amount = 0
            for item in self.items:
                if 'total_price' in item:
                    total_amount += item['total_price']

            # التحقق من صحة البيانات
            if not invoice_number:
                show_error_message("خطأ", "يجب إدخال رقم الفاتورة")
                return None

            if not client_id:
                show_error_message("خطأ", "يجب اختيار عميل")
                return None

            if not self.items:
                show_error_message("خطأ", "يجب إضافة عنصر واحد على الأقل للفاتورة")
                return None

            if paid_amount > total_amount:
                show_error_message("خطأ", "المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ الإجمالي")
                return None

            # تحديث حالة الفاتورة بناءً على المبلغ المدفوع
            if paid_amount >= total_amount:
                status = 'paid'
            elif paid_amount > 0:
                status = 'partially_paid'
            elif status != 'cancelled':
                status = 'pending'

            return {
                'invoice_number': invoice_number,
                'client_id': client_id,
                'date': date,
                'due_date': due_date,
                'total_amount': total_amount,
                'paid_amount': paid_amount,
                'status': status,
                'notes': notes,
                'items': self.items
            }
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء معالجة بيانات الفاتورة: {str(e)}")
            return None

class InvoicesWidget(QWidget):
    """واجهة إدارة الفواتير"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()

    def get_gradient_style(self, colors):
        """دالة مساعدة لإنشاء تدرجات الألوان"""
        return f"""qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 {colors[0]}, stop:0.5 {colors[1]}, stop:1 {colors[2]})"""

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات والإيرادات والمشاريع
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للموردين
        title_label = QLabel("📋 إدارة الفواتير المتطورة - نظام شامل ومتقدم لإدارة الفواتير مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للموردين
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث برقم الفاتورة، العميل أو الملاحظات...")
        self.search_edit.textChanged.connect(self.filter_invoices)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_invoices)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()


        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول الفواتير المتطور والمحسن
        self.create_advanced_invoices_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.invoices_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة فاتورة")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_invoice)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور
        self.edit_button.clicked.connect(self.edit_invoice)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_invoice)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث بسيط بدون قائمة منسدلة
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # ربط زر العرض بالوظيفة الأساسية
        self.view_button.clicked.connect(self.view_invoice)

        self.add_payment_button = QPushButton("💰 إضافة دفعة")
        self.style_advanced_button(self.add_payment_button, 'orange')  # برتقالي للدفعات
        self.add_payment_button.clicked.connect(self.add_payment)
        self.add_payment_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير مطابقة لقسم العملاء
        from ui.unified_styles import UnifiedStyles
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        pdf_report_action = QAction("📄 تقرير PDF", self)
        pdf_report_action.triggered.connect(self.export_report_pdf)
        export_menu.addAction(pdf_report_action)

        self.export_button.setMenu(export_menu)

        self.report_button = QPushButton("📋 التقارير")
        self.style_advanced_button(self.report_button, 'cyan')  # سيان مميز للتقارير
        self.report_button.clicked.connect(self.generate_invoices_report)
        self.report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي الفواتير مطور ليتشابه مع الأزرار مع حفظ المقاسات والخط الداخلي
        self.total_label = QLabel("إجمالي الفواتير: 0.00")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.add_payment_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        self.setLayout(main_layout)

        # تحميل البيانات
        self.refresh_data()

        # إصلاح نهائي لعرض عمود العميل
        QTimer.singleShot(1000, self.fix_client_column_width)

    def create_advanced_invoices_table(self):
        """
        إنشاء جدول الفواتير المتطور والمحسن مع تصميم جميل
        - إطار أسود ثابت بسماكة 3px
        - تدرجات لونية جميلة
        - أيقونات في العناوين
        - تنسيق متطور للخلايا
        - علامة مائية Smart Finish
        - تفاعل متقدم مع المستخدم
        """
        styled_table = StyledTable(auto_stretch=False)
        self.invoices_table = styled_table.table
        self.invoices_table.setColumnCount(8)
        # عناوين محسنة مع أيقونات متطورة وجذابة
        headers = [
            "🆔 الرقم التسلسلي",
            "📋 رقم الفاتورة",
            "👨‍💼 العميل",
            "📆 تاريخ الإنشاء",
            "⏳ موعد الاستحقاق",
            "💎 إجمالي المبلغ",
            "💵 المبلغ المسدد",
            "🎯 حالة الدفع"
        ]
        self.invoices_table.setHorizontalHeaderLabels(headers)

        # تحسين عرض الأعمدة - مقاسات موحدة بدون امتداد
        header = self.invoices_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # الرقم
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # رقم الفاتورة
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # العميل
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # التاريخ
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # تاريخ الدفع القادم
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # المبلغ الإجمالي
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # المبلغ المدفوع
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # الحالة

        # تحديد عرض الأعمدة المخصصة - تكبير الأعمدة بعد العميل
        self.invoices_table.setColumnWidth(0, 204)  # الرقم - تكبير إلى 204px
        self.invoices_table.setColumnWidth(1, 270)  # رقم الفاتورة - تكبير 3 درجات
        self.invoices_table.setColumnWidth(2, 310)  # العميل - تكبير 7 درجات
        self.invoices_table.setColumnWidth(3, 220)  # التاريخ - تكبير درجة واحدة
        self.invoices_table.setColumnWidth(4, 220)  # تاريخ الدفع القادم - تكبير درجة واحدة
        self.invoices_table.setColumnWidth(5, 220)  # المبلغ الإجمالي - تكبير درجة واحدة
        self.invoices_table.setColumnWidth(6, 220)  # المبلغ المدفوع - تكبير درجة واحدة
        self.invoices_table.setColumnWidth(7, 236)  # الحالة - مقاس مصحح إلى 236px

        # إصلاح عرض عمود العميل
        self.fix_client_column_width()

        # إعداد خصائص الجدول المتقدمة مع إصلاح التفاعل
        self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.invoices_table.setSelectionMode(QTableWidget.SingleSelection)  # تحديد مفرد لإصلاح المشكلة
        self.invoices_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.invoices_table.setAlternatingRowColors(False)  # تعطيل التلوين التلقائي
        self.invoices_table.setSortingEnabled(True)
        self.invoices_table.setShowGrid(True)
        self.invoices_table.setWordWrap(False)

        # إصلاح مشكلة التفاعل
        self.invoices_table.setFocusPolicy(Qt.StrongFocus)
        self.invoices_table.setEnabled(True)
        self.invoices_table.setAttribute(Qt.WA_AcceptTouchEvents, False)  # تعطيل اللمس لتجنب التداخل

        # تحسين ارتفاع الصفوف - مقلل حسب الطلب
        self.invoices_table.verticalHeader().setDefaultSectionSize(45)  # ارتفاع مقلل
        self.invoices_table.verticalHeader().setVisible(False)  # إخفاء أرقام الصفوف

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header = self.invoices_table.horizontalHeader()
        header.setFixedHeight(55)  # ارتفاع مقلل درجتين (من 75 إلى 55)
        header.setDefaultAlignment(Qt.AlignCenter)  # توسيط العناوين
        header.setMinimumSectionSize(120)  # عرض أدنى للأعمدة

        # إزالة تنسيق الخط القديم - سيتم تطبيق الخط من CSS



        # إضافة العلامة المائية للجدول
        self.add_watermark_to_invoices_table()

        # إضافة تفاعل متقدم للجدول
        self.setup_table_interactions()

        # تطبيق تصميم متطور جداً وأنيق للجدول مع الحفاظ على الإطار الأسود
        self.invoices_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين الجديد بعد تنسيق الجدول - بأولوية قصوى
        header = self.invoices_table.horizontalHeader()

        # إزالة أي تنسيق سابق وإعادة تعيين الخط
        header.setStyleSheet("")
        header.setFont(QFont())  # إعادة تعيين الخط للافتراضي

        # تأخير قصير لضمان تطبيق الإزالة
        def apply_new_style():
            header.setStyleSheet("")  # تأكيد إضافي

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;


                text-align: center !important;
                vertical-align: middle !important;
                min-height: 55px !important;
                max-height: 55px !important;
                height: 55px !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد مع تأخير للتأكد من الثبات
        header.setStyleSheet(new_header_style)

        # تأكيد إضافي بعد تأخير قصير
        QTimer.singleShot(50, apply_new_style)
        QTimer.singleShot(100, lambda: header.setStyleSheet(new_header_style))
        QTimer.singleShot(200, lambda: header.setStyleSheet(new_header_style))

    def add_watermark_to_invoices_table(self):
        """إضافة علامة مائية لجدول الفواتير"""
        try:

            # إنشاء العلامة المائية مثل العملاء والموردين
            watermark = QLabel("Smart Finish", self.invoices_table)
            watermark.setAlignment(Qt.AlignCenter)

            # تنسيق العلامة المائية مكبرة أكثر للتوحيد مع الباقي
            font = QFont("Arial", 150, QFont.Bold)
            watermark.setFont(font)

            # تطبيق تصميم محسن للعلامة المائية - أكثر وضوحاً لكن لا يؤثر على البيانات
            watermark.setStyleSheet("""
                QLabel {
                    color: rgba(71, 85, 105, 0.35);
                    background: transparent;
                    border: none;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
                    font-weight: bold;
                }
            """)

            # إصلاح مشكلة التفاعل - العلامة المائية لا تتداخل مع النقرات
            watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)
            watermark.setEnabled(False)  # تعطيل التفاعل مع العلامة المائية
            watermark.lower()  # وضع العلامة المائية في الخلف

            # تحسين إضافي للشفافية والوضوح
            watermark.setWindowOpacity(0.6)  # شفافية محسنة

            # تحديد موضع وحجم العلامة المائية مع تحسين الموضع
            def update_watermark_geometry():
                if self.invoices_table.isVisible():
                    rect = self.invoices_table.rect()
                    # تحسين الموضع ليكون في المنتصف مع مساحة أفضل
                    watermark.setGeometry(rect.x() + 50, rect.y() + 100,
                                        rect.width() - 100, rect.height() - 200)
                    watermark.lower()  # وضع العلامة المائية في الخلف دائماً
                    watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)

            # ربط تحديث الموضع بتغيير حجم الجدول
            original_resize_event = self.invoices_table.resizeEvent
            def custom_resize_event(event):
                original_resize_event(event)
                update_watermark_geometry()
            self.invoices_table.resizeEvent = custom_resize_event

            # تحديث أولي للموضع
            update_watermark_geometry()

        except Exception as e:
            print(f"خطأ في إضافة العلامة المائية: {str(e)}")

    def setup_table_interactions(self):
        """إعداد التفاعلات المتقدمة والمتطورة للجدول"""
        try:
            # إصلاح أساسي للتفاعل
            self.invoices_table.setEnabled(True)
            self.invoices_table.setFocusPolicy(Qt.StrongFocus)
            self.invoices_table.setAttribute(Qt.WA_AcceptTouchEvents, False)

            # تمكين تتبع الماوس للتأثيرات التفاعلية المتطورة
            self.invoices_table.setMouseTracking(True)

            # تمكين الميزات التفاعلية المتقدمة
            self.invoices_table.setDragDropMode(QTableWidget.NoDragDrop)
            self.invoices_table.setContextMenuPolicy(Qt.CustomContextMenu)

            # ربط الأحداث المتطورة
            self.invoices_table.doubleClicked.connect(self.edit_invoice)
            self.invoices_table.itemSelectionChanged.connect(self.on_selection_changed)
            self.invoices_table.customContextMenuRequested.connect(self.show_context_menu)

            # إضافة معالج النقر لإلغاء التحديد عند النقر على مكان فارغ
            self.invoices_table.clicked.connect(self.handle_table_click)

            # إضافة معالج النقر بالماوس للتحكم الأفضل
            original_mouse_press = self.invoices_table.mousePressEvent
            def enhanced_mouse_press(event):
                # الحصول على العنصر في موقع النقر
                item = self.invoices_table.itemAt(event.pos())
                if not item:
                    # النقر على مكان فارغ، إلغاء التحديد
                    self.invoices_table.clearSelection()
                else:
                    # استدعاء المعالج الأصلي
                    original_mouse_press(event)

            self.invoices_table.mousePressEvent = enhanced_mouse_press

            # إضافة تلميحات متطورة للخلايا
            self.invoices_table.cellEntered.connect(self.show_advanced_cell_tooltip)

            # تحسين سلوك التمرير
            self.invoices_table.setHorizontalScrollMode(QTableWidget.ScrollPerPixel)
            self.invoices_table.setVerticalScrollMode(QTableWidget.ScrollPerPixel)

        except Exception as e:
            print(f"❌ خطأ في إعداد تفاعلات الجدول: {str(e)}")

    def show_advanced_cell_tooltip(self, row, column):
        """عرض تلميح متطور ومفصل للخلية"""
        try:
            item = self.invoices_table.item(row, column)
            if not item:
                return

            # تلميحات متطورة ومفصلة حسب العمود
            column_tooltips = {
                0: f"🆔 الرقم التسلسلي: {item.text()}\n📋 معرف فريد للفاتورة\n💡 يستخدم للمراجع الداخلية",
                1: f"📋 رقم الفاتورة: {item.text()}\n🔍 الرقم الرسمي للفاتورة\n💡 انقر نقرتين للتعديل\n🖱️ انقر بالزر الأيمن للخيارات",
                2: f"👨‍💼 العميل: {item.text()}\n📞 معلومات العميل المرتبط\n💡 انقر نقرتين لعرض ملف العميل\n📊 عرض تاريخ التعاملات",
                3: f"📆 تاريخ الإنشاء: {item.text()}\n🕐 تاريخ إنشاء الفاتورة\n📅 يحدد بداية فترة الدفع",
                4: f"⏳ موعد الاستحقاق: {item.text()}\n⚠️ الموعد النهائي للدفع\n🔔 تحقق من التواريخ المتأخرة\n📨 إرسال تذكير للعميل",
                5: f"💎 المبلغ الإجمالي: {item.text()}\n💰 إجمالي قيمة الفاتورة\n📊 يشمل جميع البنود والضرائب",
                6: f"💵 المبلغ المسدد: {item.text()}\n✅ المبلغ المدفوع فعلياً\n📈 تتبع تقدم السداد\n💡 انقر لإضافة دفعة جديدة",
                7: f"🎯 حالة الدفع: {item.text()}\n📊 الوضع الحالي للفاتورة\n🔄 يتم تحديثها تلقائياً\n📋 عرض تفاصيل الحالة"
            }

            tooltip = column_tooltips.get(column, f"📄 القيمة: {item.text()}\n💡 انقر للمزيد من الخيارات")
            item.setToolTip(tooltip)

        except Exception as e:
            pass

    def show_context_menu(self, position):
        """عرض قائمة سياق متطورة"""
        try:
            item = self.invoices_table.itemAt(position)
            if item:
                from PyQt5.QtWidgets import QMenu, QAction
                menu = QMenu(self.invoices_table)

                # إضافة خيارات القائمة
                edit_action = QAction("✏️ تعديل الفاتورة", self)
                view_action = QAction("👁️ عرض التفاصيل", self)
                payment_action = QAction("💰 إضافة دفعة", self)
                print_action = QAction("🖨️ طباعة", self)

                menu.addAction(edit_action)
                menu.addAction(view_action)
                menu.addAction(payment_action)
                menu.addAction(print_action)

                # عرض القائمة
                menu.exec_(self.invoices_table.mapToGlobal(position))

        except Exception as e:
            pass

    def handle_table_click(self, index):
        """معالج النقر على الجدول لإلغاء التحديد عند النقر على مكان فارغ"""
        try:
            # التحقق من صحة الفهرس
            if not index.isValid():
                # النقر على مكان فارغ خارج الصفوف
                self.invoices_table.clearSelection()
                return

            # التحقق من وجود بيانات في الصف المنقور
            row = index.row()
            if row >= self.invoices_table.rowCount():
                # النقر على صف غير موجود
                self.invoices_table.clearSelection()
                return

            # التحقق من وجود عنصر في العمود الأول (الرقم التسلسلي)
            first_item = self.invoices_table.item(row, 0)
            if not first_item or not first_item.text().strip():
                # الصف فارغ، إلغاء التحديد
                self.invoices_table.clearSelection()

        except Exception as e:
            print(f"خطأ في معالج النقر: {str(e)}")

    def on_selection_changed(self):
        """معالج تغيير التحديد في الجدول"""
        try:
            selected_rows = len(self.invoices_table.selectionModel().selectedRows())
            # يمكن إضافة منطق إضافي هنا حسب الحاجة
        except Exception as e:
            print(f"خطأ في معالج تغيير التحديد: {str(e)}")



    def get_selected_invoice_id(self):
        """استخراج معرف الفاتورة المحددة من الجدول"""
        try:
            selected_row = self.invoices_table.currentRow()
            if selected_row < 0:
                return None, "الرجاء اختيار فاتورة من القائمة"

            if not self.invoices_table.item(selected_row, 0):
                return None, "الرجاء اختيار فاتورة صالحة من القائمة"

            # استخراج الرقم من النص (إزالة # والأيقونات)
            id_text = self.invoices_table.item(selected_row, 0).text()
            numbers = re.findall(r'\d+', id_text)
            if not numbers:
                return None, "لا يمكن استخراج رقم الفاتورة"

            return int(numbers[0]), None
        except Exception as e:
            return None, f"خطأ في استخراج معرف الفاتورة: {str(e)}"

    def fix_client_column_width(self):
        """إصلاح عرض عمود العميل"""
        try:
            header = self.invoices_table.horizontalHeader()
            header.setSectionResizeMode(2, QHeaderView.Fixed)
            self.invoices_table.setColumnWidth(2, 310)
            header.resizeSection(2, 310)
        except Exception as e:
            pass

    def refresh_data(self):
        """تحديث بيانات الفواتير في الجدول"""
        try:
            # الحصول على جميع الفواتير من قاعدة البيانات
            invoices = self.session.query(Invoice).all()
            self.populate_table(invoices)
            self.update_summary(invoices)

            # إصلاح عرض عمود العميل بعد تحديث البيانات
            self.fix_client_column_width()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث البيانات: {str(e)}")



    def preview_invoice(self):
        """معاينة سريعة للفاتورة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            # إنشاء نافذة معاينة سريعة
            preview_dialog = QDialog(self)
            preview_dialog.setWindowTitle(f"معاينة سريعة - فاتورة {invoice.invoice_number}")
            preview_dialog.setMinimumSize(400, 300)

            layout = QVBoxLayout()

            # معلومات أساسية
            info_text = f"""
            📋 رقم الفاتورة: {invoice.invoice_number}
            👤 العميل: {invoice.client.name if invoice.client else 'غير محدد'}
            📅 التاريخ: {invoice.date.strftime('%Y-%m-%d') if invoice.date else 'غير محدد'}
            💰 المبلغ الإجمالي: {format_currency(invoice.total_amount)}
            💵 المبلغ المدفوع: {format_currency(invoice.paid_amount)}
            📊 الحالة: {invoice.status or 'غير محددة'}
            """

            info_label = QLabel(info_text)
            info_label.setStyleSheet("font-size: 14px; padding: 10px;")
            layout.addWidget(info_label)

            # زر إغلاق
            close_button = QPushButton("إغلاق")
            close_button.clicked.connect(preview_dialog.accept)
            layout.addWidget(close_button)

            preview_dialog.setLayout(layout)
            preview_dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في المعاينة: {str(e)}")



    def populate_table(self, invoices):
        """ملء جدول الفواتير بالبيانات"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.invoices_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.invoices_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن
            for row, invoice in enumerate(invoices):
                try:
                    self.invoices_table.insertRow(row)

                    # 1. الرقم التسلسلي مع تنسيق متطور وأيقونة
                    id_item = QTableWidgetItem(f"🆔 #{str(invoice.id).zfill(4)}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setFont(QFont("Consolas", 12, QFont.Bold))
                    id_item.setForeground(QColor("#0f172a"))
                    id_item.setToolTip(f"🆔 الرقم التسلسلي: {invoice.id}")
                    self.invoices_table.setItem(row, 0, id_item)

                    # 2. رقم الفاتورة مع تنسيق احترافي
                    invoice_num = invoice.invoice_number or f"INV-{str(invoice.id).zfill(6)}"
                    invoice_num_item = QTableWidgetItem(f"📋 {invoice_num}")
                    invoice_num_item.setTextAlignment(Qt.AlignCenter)
                    invoice_num_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    invoice_num_item.setForeground(QColor("#1e40af"))
                    invoice_num_item.setToolTip(f"📋 رقم الفاتورة: {invoice_num}\n💡 انقر نقرتين للتعديل")
                    self.invoices_table.setItem(row, 1, invoice_num_item)

                    # 3. العميل مع تنسيق جذاب
                    try:
                        client_name = invoice.client.name if invoice.client else "عميل غير محدد"
                    except Exception:
                        client_name = "عميل غير محدد"
                    client_item = QTableWidgetItem(f"👤 {client_name}")
                    client_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    client_item.setForeground(QColor("#065f46"))
                    client_item.setToolTip(f"👨‍💼 العميل: {client_name}\n💡 انقر نقرتين لعرض تفاصيل العميل")
                    self.invoices_table.setItem(row, 2, client_item)

                    # 4. تاريخ الإنشاء مع تنسيق أنيق
                    try:
                        if invoice.date:
                            date_formatted = invoice.date.strftime("%d/%m/%Y")
                            date_display = f"📆 {date_formatted}"
                        else:
                            date_display = "📆 غير محدد"
                    except Exception:
                        date_display = "📆 غير محدد"
                    date_item = QTableWidgetItem(date_display)
                    date_item.setTextAlignment(Qt.AlignCenter)
                    date_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    date_item.setForeground(QColor("#374151"))
                    date_item.setToolTip(f"📆 تاريخ إنشاء الفاتورة: {date_display}")
                    self.invoices_table.setItem(row, 3, date_item)

                    # 5. تاريخ الاستحقاق مع تحذير بصري
                    try:
                        if invoice.due_date:
                            due_date_formatted = invoice.due_date.strftime("%d/%m/%Y")
                            # فحص إذا كان التاريخ متأخر
                            import datetime
                            if invoice.due_date < datetime.datetime.now():
                                due_date_display = f"⚠️ {due_date_formatted}"
                                due_color = QColor("#dc2626")  # أحمر للمتأخر
                            else:
                                due_date_display = f"⏳ {due_date_formatted}"
                                due_color = QColor("#059669")  # أخضر للعادي
                        else:
                            due_date_display = "⏳ غير محدد"
                            due_color = QColor("#6b7280")
                    except Exception:
                        due_date_display = "⏳ غير محدد"
                        due_color = QColor("#6b7280")
                    due_date_item = QTableWidgetItem(due_date_display)
                    due_date_item.setTextAlignment(Qt.AlignCenter)
                    due_date_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    due_date_item.setForeground(due_color)
                    due_date_item.setToolTip(f"⏳ موعد استحقاق الدفع: {due_date_display}")
                    self.invoices_table.setItem(row, 4, due_date_item)

                    # 6. المبلغ الإجمالي مع تنسيق مالي متطور
                    try:
                        if invoice.total_amount and invoice.total_amount > 0:
                            total_formatted = f"{int(invoice.total_amount):,}".replace(',', '٬')
                            total_display = f"💎 {total_formatted} جنيه"
                            total_color = QColor("#0f172a")
                        else:
                            total_display = "💎 0 جنيه"
                            total_color = QColor("#6b7280")
                    except Exception:
                        total_display = "💎 0 جنيه"
                        total_color = QColor("#6b7280")
                    total_item = QTableWidgetItem(total_display)
                    total_item.setTextAlignment(Qt.AlignCenter)
                    total_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    total_item.setForeground(total_color)
                    total_item.setToolTip(f"💎 إجمالي مبلغ الفاتورة: {total_display}")
                    self.invoices_table.setItem(row, 5, total_item)

                    # 7. المبلغ المدفوع مع مؤشر التقدم
                    try:
                        if invoice.paid_amount and invoice.paid_amount > 0:
                            paid_formatted = f"{int(invoice.paid_amount):,}".replace(',', '٬')
                            # حساب نسبة الدفع
                            if invoice.total_amount and invoice.total_amount > 0:
                                payment_ratio = (invoice.paid_amount / invoice.total_amount) * 100
                                if payment_ratio >= 100:
                                    paid_display = f"💵 {paid_formatted} جنيه ✅"
                                    paid_color = QColor("#059669")
                                elif payment_ratio >= 50:
                                    paid_display = f"💵 {paid_formatted} جنيه 🔄"
                                    paid_color = QColor("#0891b2")
                                else:
                                    paid_display = f"💵 {paid_formatted} جنيه ⏳"
                                    paid_color = QColor("#dc2626")
                            else:
                                paid_display = f"💵 {paid_formatted} جنيه"
                                paid_color = QColor("#059669")
                        else:
                            paid_display = "💵 0 جنيه ❌"
                            paid_color = QColor("#ef4444")
                    except Exception:
                        paid_display = "💵 0 جنيه"
                        paid_color = QColor("#6b7280")
                    paid_item = QTableWidgetItem(paid_display)
                    paid_item.setTextAlignment(Qt.AlignCenter)
                    paid_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    paid_item.setForeground(paid_color)
                    paid_item.setToolTip(f"💵 المبلغ المسدد: {paid_display}")
                    self.invoices_table.setItem(row, 6, paid_item)

                    # 8. حالة الدفع مع تصميم متطور ومؤشرات بصرية
                    status_map = {
                        'pending': {
                            'text': '🎯 قيد الانتظار',
                            'color': QColor("#d97706"),
                            'tooltip': '🎯 الفاتورة في انتظار الدفع\n💡 تحتاج متابعة مع العميل'
                        },
                        'paid': {
                            'text': '🎉 مدفوعة بالكامل',
                            'color': QColor("#059669"),
                            'tooltip': '🎉 تم سداد الفاتورة بالكامل\n✅ العملية مكتملة'
                        },
                        'partially_paid': {
                            'text': '⚡ مدفوعة جزئياً',
                            'color': QColor("#0284c7"),
                            'tooltip': '⚡ تم سداد جزء من المبلغ\n🔄 يتطلب متابعة للباقي'
                        },
                        'cancelled': {
                            'text': '🚫 ملغاة',
                            'color': QColor("#dc2626"),
                            'tooltip': '🚫 تم إلغاء الفاتورة\n❌ لا تحتاج متابعة'
                        }
                    }

                    status_info = status_map.get(invoice.status, {
                        'text': f"❓ {invoice.status or 'غير محدد'}",
                        'color': QColor("#6b7280"),
                        'tooltip': f"❓ حالة غير معروفة: {invoice.status or 'غير محدد'}"
                    })

                    status_item = QTableWidgetItem(status_info['text'])
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    status_item.setForeground(status_info['color'])
                    status_item.setToolTip(status_info['tooltip'])
                    self.invoices_table.setItem(row, 7, status_item)
                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تمكين تحديث الجدول
            self.invoices_table.setUpdatesEnabled(True)

            # إصلاح عرض عمود العميل بعد تحديث البيانات
            self.fix_client_column_width()
        except Exception as e:
            # إعادة تمكين تحديث الجدول في حالة حدوث خطأ
            self.invoices_table.setUpdatesEnabled(True)
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث جدول الفواتير: {str(e)}")

    def update_summary(self, invoices):
        """تحديث ملخص الفواتير"""
        try:
            # حساب المبالغ بطريقة آمنة
            total = 0
            paid = 0

            for invoice in invoices:
                try:
                    if hasattr(invoice, 'total_amount') and invoice.total_amount is not None:
                        total += invoice.total_amount
                except Exception:
                    pass

                try:
                    if hasattr(invoice, 'paid_amount') and invoice.paid_amount is not None:
                        paid += invoice.paid_amount
                except Exception:
                    pass

            balance = total - paid

            # تحديث النصوص
            self.total_label.setText(f"إجمالي الفواتير: {format_currency(total)} | المدفوعات: {format_currency(paid)} | المستحقات: {format_currency(balance)}")
        except Exception as e:
            # في حالة حدوث خطأ، عرض قيم افتراضية
            self.total_label.setText("إجمالي الفواتير: 0.00 | المدفوعات: 0.00 | المستحقات: 0.00")
            print(f"خطأ في تحديث ملخص الفواتير: {str(e)}")

    def filter_invoices(self):
        """تصفية الفواتير بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = getattr(self, 'current_filter_value', None)

            # بناء الاستعلام
            try:
                query = self.session.query(Invoice).join(Client, Invoice.client_id == Client.id, isouter=True)

                # تطبيق تصفية النص
                if search_text:
                    query = query.filter(
                        Invoice.invoice_number.like(f"%{search_text}%") |
                        Client.name.like(f"%{search_text}%") |
                        Invoice.notes.like(f"%{search_text}%")
                    )

                # تطبيق تصفية الحالة
                if status:
                    query = query.filter(Invoice.status == status)

                # تنفيذ الاستعلام
                invoices = query.all()
            except Exception as query_error:
                # في حالة فشل الاستعلام، استخدم قائمة فارغة
                print(f"خطأ في استعلام الفواتير: {str(query_error)}")
                invoices = []

            # تحديث الجدول والملخص
            self.populate_table(invoices)
            self.update_summary(invoices)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصفية الفواتير: {str(e)}")

    def add_invoice(self):
        """إنشاء فاتورة جديدة"""
        try:
            dialog = InvoiceDialog(self, session=self.session)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # استخراج عناصر الفاتورة
                    items_data = data.pop('items')

                    # إنشاء فاتورة جديدة في قاعدة البيانات
                    invoice = Invoice(**data)
                    self.session.add(invoice)
                    self.session.flush()  # للحصول على معرف الفاتورة

                    # إضافة عناصر الفاتورة
                    for item_data in items_data:
                        # التأكد من وجود جميع البيانات المطلوبة
                        if 'description' in item_data and 'quantity' in item_data and 'unit_price' in item_data and 'total_price' in item_data:
                            item_data['invoice_id'] = invoice.id
                            item = InvoiceItem(**item_data)
                            self.session.add(item)

                    self.session.commit()

                    show_info_message("تم", "تم إنشاء الفاتورة بنجاح")
                    self.refresh_data()
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء الفاتورة: {str(e)}")

    def edit_invoice(self):
        """تعديل بيانات فاتورة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            dialog = InvoiceDialog(self, invoice, self.session)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # استخراج عناصر الفاتورة
                    items_data = data.pop('items')

                    # تحديث بيانات الفاتورة
                    for key, value in data.items():
                        setattr(invoice, key, value)

                    # حذف جميع عناصر الفاتورة الحالية
                    for item in invoice.items:
                        self.session.delete(item)

                    # إضافة عناصر الفاتورة الجديدة
                    for item_data in items_data:
                        # التأكد من وجود جميع البيانات المطلوبة
                        if 'description' in item_data and 'quantity' in item_data and 'unit_price' in item_data and 'total_price' in item_data:
                            item_data['invoice_id'] = invoice.id
                            item = InvoiceItem(**item_data)
                            self.session.add(item)

                    self.session.commit()
                    show_info_message("تم", "تم تحديث بيانات الفاتورة بنجاح")
                    self.refresh_data()
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء تعديل الفاتورة: {str(e)}")

    def delete_invoice(self):
        """حذف فاتورة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            # التحقق من وجود إيرادات مرتبطة بالفاتورة
            if invoice.revenues:
                show_error_message(
                    "خطأ",
                    f"لا يمكن حذف الفاتورة لأنها مرتبطة بـ {len(invoice.revenues)} إيراد. قم بحذف الإيرادات أولاً."
                )
                return

            # طلب تأكيد الحذف
            if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف الفاتورة {invoice.invoice_number}؟"):
                # حذف جميع عناصر الفاتورة
                for item in invoice.items:
                    self.session.delete(item)

                # حذف الفاتورة
                self.session.delete(invoice)
                self.session.commit()
                show_info_message("تم", "تم حذف الفاتورة بنجاح")
                self.refresh_data()
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء حذف الفاتورة: {str(e)}")

    def view_invoice(self):
        """عرض تفاصيل الفاتورة"""
        invoice = None
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء عرض الفاتورة: {str(e)}")
            return

        try:
            # إنشاء نافذة لعرض تفاصيل الفاتورة
            dialog = QDialog(self)
            dialog.setWindowTitle(f"تفاصيل الفاتورة - {invoice.invoice_number if invoice else ''}")
            dialog.setMinimumSize(800, 600)

            layout = QVBoxLayout()

            # إنشاء مستعرض نصي لعرض تفاصيل الفاتورة
            text_browser = QTextBrowser()
            text_browser.setOpenExternalLinks(False)

            # إنشاء محتوى HTML للفاتورة
            try:
                html_content = self.generate_invoice_html(invoice)
                text_browser.setHtml(html_content)
            except Exception as html_error:
                error_message = f"حدث خطأ أثناء إنشاء محتوى الفاتورة: {str(html_error)}"
                print(error_message)
                text_browser.setHtml(f"""
                <html dir="rtl">
                <body>
                    <h1>خطأ في عرض الفاتورة</h1>
                    <p>{error_message}</p>
                </body>
                </html>
                """)

            layout.addWidget(text_browser)

            # أزرار الإجراءات
            button_layout = QHBoxLayout()
            button_layout.setSpacing(10)

            # تعيين نمط موحد للأزرار
            button_style = """
                QPushButton {
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 16px;
                    min-height: 35px;
                    border-radius: 5px;
                    border: none;
                    min-width: 120px;
                }
                QPushButton:hover {
                    opacity: 0.8;
                }
                QPushButton:pressed {
                    opacity: 0.7;
                }
            """

            # زر تصدير PDF
            export_pdf_button = StyledButton("📤 تصدير", "warning", "normal")
            export_pdf_button.clicked.connect(lambda: self.export_invoice_to_pdf(invoice, dialog))
            export_pdf_button.setStyleSheet(button_style + "background-color: #e74c3c; color: white;")

            # زر طباعة
            print_button = QPushButton("طباعة")
            print_button.clicked.connect(lambda: self.print_invoice_from_dialog(invoice))
            print_button.setStyleSheet(button_style + "background-color: #3498db; color: white;")

            # زر إغلاق
            close_button = QPushButton("إغلاق")
            close_button.clicked.connect(dialog.accept)
            close_button.setStyleSheet(button_style + "background-color: #95a5a6; color: white;")

            button_layout.addStretch()
            button_layout.addWidget(export_pdf_button.button)
            button_layout.addWidget(print_button)
            button_layout.addWidget(close_button)

            layout.addLayout(button_layout)
            dialog.setLayout(layout)

            dialog.exec_()
        except Exception as dialog_error:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء نافذة عرض الفاتورة: {str(dialog_error)}")

    def print_invoice(self):
        """طباعة الفاتورة"""
        invoice = None
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحضير الفاتورة للطباعة: {str(e)}")
            return

        try:
            # إنشاء مستند للطباعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)

            # إظهار مربع حوار الطباعة
            dialog = QPrintDialog(printer, self)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # إنشاء مستند نصي
                    document = QTextDocument()

                    # إنشاء محتوى HTML للفاتورة
                    try:
                        html_content = self.generate_invoice_html(invoice)
                        document.setHtml(html_content)
                    except Exception as html_error:
                        error_message = f"حدث خطأ أثناء إنشاء محتوى الفاتورة للطباعة: {str(html_error)}"
                        print(error_message)
                        document.setHtml(f"""
                        <html dir="rtl">
                        <body>
                            <h1>خطأ في طباعة الفاتورة</h1>
                            <p>{error_message}</p>
                        </body>
                        </html>
                        """)

                    # طباعة المستند
                    document.print_(printer)
                except Exception as print_error:
                    show_error_message("خطأ", f"حدث خطأ أثناء طباعة المستند: {str(print_error)}")
        except Exception as dialog_error:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء مربع حوار الطباعة: {str(dialog_error)}")

    def generate_invoice_html(self, invoice):
        """إنشاء محتوى HTML للفاتورة"""
        try:
            # ترجمة حالة الفاتورة
            status_map = {
                'pending': 'قيد الانتظار',
                'paid': 'مدفوعة',
                'partially_paid': 'مدفوعة جزئيًا',
                'cancelled': 'ملغاة'
            }
            status_text = status_map.get(invoice.status, invoice.status or "")

            # تنسيق التواريخ بطريقة آمنة
            try:
                date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else ""
            except Exception:
                date_str = ""

            try:
                due_date_str = invoice.due_date.strftime("%Y-%m-%d") if invoice.due_date else ""
            except Exception:
                due_date_str = ""

            # إنشاء جدول عناصر الفاتورة
            items_html = """
            <table border="1" cellpadding="5" cellspacing="0" width="100%">
                <tr style="background-color: #f2f2f2;">
                    <th>السعر الإجمالي</th>
                    <th>سعر الوحدة</th>
                    <th>الكمية</th>
                    <th>الوصف</th>
                </tr>
            """

            # التحقق من وجود عناصر الفاتورة
            if hasattr(invoice, 'items') and invoice.items:
                for item in invoice.items:
                    try:
                        description = item.description or ""
                        quantity = format_quantity(item.quantity) if hasattr(item, 'quantity') else "0"
                        unit_price = format_currency(item.unit_price) if hasattr(item, 'unit_price') else "0"
                        total_price = format_currency(item.total_price) if hasattr(item, 'total_price') else "0"

                        items_html += f"""
                        <tr>
                            <td>{total_price}</td>
                            <td>{unit_price}</td>
                            <td>{quantity}</td>
                            <td>{description}</td>
                        </tr>
                        """
                    except Exception as item_error:
                        print(f"خطأ في عنصر الفاتورة: {str(item_error)}")
                        continue
            else:
                items_html += """
                <tr>
                    <td colspan="4" style="text-align: center;">لا توجد عناصر</td>
                </tr>
                """

            items_html += "</table>"

            # حساب الرصيد المتبقي بطريقة آمنة
            try:
                total_amount = invoice.total_amount if hasattr(invoice, 'total_amount') and invoice.total_amount is not None else 0
                paid_amount = invoice.paid_amount if hasattr(invoice, 'paid_amount') and invoice.paid_amount is not None else 0
                balance = total_amount - paid_amount
            except Exception:
                total_amount = 0
                paid_amount = 0
                balance = 0

            # الحصول على بيانات العميل بطريقة آمنة
            client_name = ""
            client_address = ""
            client_phone = ""

            try:
                if hasattr(invoice, 'client') and invoice.client:
                    client_name = invoice.client.name if hasattr(invoice.client, 'name') else ""
                    client_address = invoice.client.address if hasattr(invoice.client, 'address') else ""
                    client_phone = invoice.client.phone if hasattr(invoice.client, 'phone') else ""
            except Exception as client_error:
                print(f"خطأ في بيانات العميل: {str(client_error)}")

            # إنشاء محتوى HTML كامل للفاتورة
            html = f"""
            <html dir="rtl">
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; }}
                    .invoice-header {{ text-align: center; margin-bottom: 20px; }}
                    .invoice-details {{ margin-bottom: 20px; }}
                    .invoice-details table {{ width: 100%; }}
                    .invoice-items {{ margin-bottom: 20px; }}
                    .invoice-summary {{ text-align: left; margin-top: 20px; }}
                    .invoice-notes {{ margin-top: 30px; border-top: 1px solid #ccc; padding-top: 10px; }}
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <h1>فاتورة</h1>
                    <h2>{invoice.invoice_number if hasattr(invoice, 'invoice_number') else ""}</h2>
                </div>

                <div class="invoice-details">
                    <table width="100%" style="border-collapse: collapse;">
                        <tr>
                            <td width="33%" style="text-align: right;"><strong>العميل:</strong> {client_name}</td>
                            <td width="34%" style="text-align: center;"><strong>العنوان:</strong> {client_address}</td>
                            <td width="33%" style="text-align: left;"><strong>التاريخ:</strong> {date_str}</td>
                        </tr>
                        <tr>
                            <td style="text-align: right;"><strong>الهاتف:</strong> {client_phone}</td>
                            <td style="text-align: center;"><strong>الحالة:</strong> {status_text}</td>
                            <td style="text-align: left;"><strong>تاريخ الدفع القادم:</strong> {due_date_str}</td>
                        </tr>
                    </table>
                </div>

                <div class="invoice-items">
                    <h3>عناصر الفاتورة</h3>
                    {items_html}
                </div>

                <div class="invoice-summary">
                    <p><strong>المبلغ الإجمالي:</strong> {format_currency(total_amount)}</p>
                    <p><strong>المبلغ المدفوع:</strong> {format_currency(paid_amount)}</p>
                    <p><strong>الرصيد المتبقي:</strong> {format_currency(balance)}</p>
                </div>

                <div class="invoice-notes">
                    <h3>ملاحظات</h3>
                    <p>{invoice.notes or ""}</p>
                </div>
            </body>
            </html>
            """

            return html
        except Exception as e:
            print(f"خطأ في إنشاء محتوى HTML للفاتورة: {str(e)}")
            # إرجاع محتوى HTML بسيط في حالة حدوث خطأ
            return f"""
            <html dir="rtl">
            <body>
                <h1>خطأ في عرض الفاتورة</h1>
                <p>حدث خطأ أثناء محاولة عرض تفاصيل الفاتورة. الرجاء المحاولة مرة أخرى.</p>
                <p>رسالة الخطأ: {str(e)}</p>
            </body>
            </html>
            """

    def add_payment(self):
        """إضافة دفعة للفاتورة المحددة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            # التحقق من حالة الفاتورة
            if invoice.status == 'cancelled':
                show_error_message("خطأ", "لا يمكن إضافة دفعة لفاتورة ملغاة")
                return

            if invoice.status == 'paid':
                show_error_message("خطأ", "الفاتورة مدفوعة بالكامل بالفعل")
                return

            # حساب الرصيد المتبقي
            balance = invoice.total_amount - invoice.paid_amount

            # التحقق من أن الرصيد المتبقي أكبر من صفر
            if balance <= 0:
                show_error_message("خطأ", "لا يوجد رصيد متبقي للفاتورة")
                return
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحضير إضافة دفعة: {str(e)}")
            return

        # إنشاء نافذة حوار لإضافة دفعة
        dialog = QDialog(self)
        dialog.setWindowTitle(f"إضافة دفعة للفاتورة {invoice.invoice_number}")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout()

        form_layout = QFormLayout()

        # عرض معلومات الفاتورة
        invoice_label = QLabel(f"{invoice.invoice_number} - {invoice.client.name if invoice.client else ''}")
        form_layout.addRow("الفاتورة:", invoice_label)

        total_label = QLabel(format_currency(invoice.total_amount))
        form_layout.addRow("المبلغ الإجمالي:", total_label)

        paid_label = QLabel(format_currency(invoice.paid_amount))
        form_layout.addRow("المبلغ المدفوع:", paid_label)

        balance_label = QLabel(format_currency(balance))
        form_layout.addRow("الرصيد المتبقي:", balance_label)

        # حقل مبلغ الدفعة
        amount_edit = QDoubleSpinBox()
        amount_edit.setRange(1, balance)  # الحد الأدنى 1 بدلاً من 0.01
        amount_edit.setDecimals(0)  # بدون كسور عشرية
        amount_edit.setSingleStep(100)
        amount_edit.setValue(balance)  # افتراضي: المبلغ المتبقي بالكامل
        form_layout.addRow("مبلغ الدفعة:", amount_edit)

        # حقل تاريخ الدفع
        date_edit = QDateEdit()
        date_edit.setCalendarPopup(True)
        date_edit.setDate(QDate.currentDate())
        form_layout.addRow("تاريخ الدفع:", date_edit)

        # حقل الملاحظات
        notes_edit = QTextEdit()
        notes_edit.setPlaceholderText("ملاحظات حول الدفعة...")
        form_layout.addRow("ملاحظات:", notes_edit)

        layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        save_button = StyledButton("💾 حفظ", "success", "normal")
        save_button.clicked.connect(dialog.accept)

        cancel_button = StyledButton("❌ إلغاء", "secondary", "normal")
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(save_button.button)
        button_layout.addWidget(cancel_button.button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        try:
            if dialog.exec_() == QDialog.Accepted:
                amount = amount_edit.value()
                payment_date = qdate_to_datetime(date_edit.date())
                notes = notes_edit.toPlainText().strip()

                if amount <= 0:
                    show_error_message("خطأ", "يجب أن يكون مبلغ الدفعة أكبر من صفر")
                    return

                if amount > balance:
                    show_error_message("خطأ", "مبلغ الدفعة لا يمكن أن يكون أكبر من الرصيد المتبقي")
                    return

                # إنشاء إيراد جديد للدفعة
                revenue = Revenue(
                    title=f"دفعة للفاتورة {invoice.invoice_number}",
                    amount=amount,
                    date=payment_date,
                    category="مبيعات",
                    invoice_id=invoice.id,
                    notes=notes
                )

                # إضافة الإيراد إلى قاعدة البيانات
                self.session.add(revenue)

                # تحديث المبلغ المدفوع وحالة الفاتورة
                invoice.paid_amount += amount

                if invoice.paid_amount >= invoice.total_amount:
                    invoice.status = 'paid'
                else:
                    invoice.status = 'partially_paid'

                # حفظ التغييرات
                self.session.commit()

                # تحديث البيانات
                show_info_message("تم", "تم إضافة الدفعة بنجاح")
                self.refresh_data()
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء إضافة الدفعة: {str(e)}")

    def export_invoice_to_pdf(self, invoice, parent_dialog=None):
        """تصدير الفاتورة إلى ملف PDF"""
        try:
            # اختيار مكان حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                parent_dialog or self,
                "حفظ الفاتورة كـ PDF",
                f"فاتورة_{invoice.invoice_number}.pdf",
                "PDF Files (*.pdf)"
            )

            if not file_path:
                return  # المستخدم ألغى العملية

            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء مستند نصي
            document = QTextDocument()

            # إنشاء محتوى HTML للفاتورة
            try:
                html_content = self.generate_invoice_html(invoice)
                document.setHtml(html_content)
            except Exception as html_error:
                error_message = f"حدث خطأ أثناء إنشاء محتوى الفاتورة: {str(html_error)}"
                print(error_message)
                document.setHtml(f"""
                <html dir="rtl">
                <body>
                    <h1>خطأ في تصدير الفاتورة</h1>
                    <p>{error_message}</p>
                </body>
                </html>
                """)

            # طباعة المستند إلى PDF
            document.print_(printer)

            show_info_message("تم", f"تم تصدير الفاتورة بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير الفاتورة إلى PDF: {str(e)}")

    def print_invoice_from_dialog(self, invoice):
        """طباعة الفاتورة من نافذة التفاصيل"""
        try:
            # إنشاء مستند للطباعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)

            # إظهار مربع حوار الطباعة
            dialog = QPrintDialog(printer, self)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # إنشاء مستند نصي
                    document = QTextDocument()

                    # إنشاء محتوى HTML للفاتورة
                    try:
                        html_content = self.generate_invoice_html(invoice)
                        document.setHtml(html_content)
                    except Exception as html_error:
                        error_message = f"حدث خطأ أثناء إنشاء محتوى الفاتورة للطباعة: {str(html_error)}"
                        print(error_message)
                        document.setHtml(f"""
                        <html dir="rtl">
                        <body>
                            <h1>خطأ في طباعة الفاتورة</h1>
                            <p>{error_message}</p>
                        </body>
                        </html>
                        """)

                    # طباعة المستند
                    document.print_(printer)
                except Exception as print_error:
                    show_error_message("خطأ", f"حدث خطأ أثناء طباعة المستند: {str(print_error)}")
        except Exception as dialog_error:
            show_error_message("خطأ", f"حدث خطأ أثناء إنشاء مربع حوار الطباعة: {str(dialog_error)}")

    def export_to_excel(self):
        """تصدير بيانات الفواتير إلى Excel"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", "الفواتير.csv", "CSV Files (*.csv)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'المبلغ المتبقي', 'الحالة', 'طريقة الدفع'])

                    # كتابة البيانات
                    for invoice in invoices:
                        date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else ""
                        client_name = invoice.client.name if invoice.client else ""
                        remaining = invoice.total_amount - invoice.paid_amount

                        status_names = {
                            'pending': 'معلقة',
                            'partially_paid': 'مدفوعة جزئياً',
                            'paid': 'مدفوعة بالكامل'
                        }
                        status_text = status_names.get(invoice.status, invoice.status or 'معلقة')

                        payment_methods = {
                            'cash': 'نقدي',
                            'credit': 'آجل',
                            'bank_transfer': 'تحويل بنكي',
                            'check': 'شيك'
                        }
                        payment_text = payment_methods.get(invoice.payment_method, invoice.payment_method or '')

                        writer.writerow([
                            invoice.invoice_number or f"INV-{invoice.id}",
                            client_name,
                            date_str,
                            invoice.total_amount,
                            invoice.paid_amount,
                            remaining,
                            status_text,
                            payment_text
                        ])

                show_info_message("تم", f"تم تصدير الفواتير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_csv(self):
        """تصدير بيانات الفواتير إلى CSV"""
        try:
            import csv

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_الفواتير.csv", "ملفات CSV (*.csv)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.invoices_table.columnCount()):
                headers.append(self.invoices_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.invoices_table.rowCount()):
                row_data = []
                for col in range(self.invoices_table.columnCount()):
                    item = self.invoices_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_to_json(self):
        """تصدير بيانات الفواتير إلى JSON"""
        try:
            import json

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "قائمة_الفواتير.json", "ملفات JSON (*.json)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.invoices_table.columnCount()):
                headers.append(self.invoices_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.invoices_table.rowCount()):
                row_data = {}
                for col in range(self.invoices_table.columnCount()):
                    item = self.invoices_table.item(row, col)
                    row_data[headers[col]] = item.text() if item else ""
                data.append(row_data)

            # كتابة البيانات إلى ملف JSON
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_invoice_pdf(self):
        """تصدير الفاتورة المحددة إلى PDF"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            self.export_invoice_to_pdf(invoice)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير الفاتورة: {str(e)}")

    def duplicate_invoice(self):
        """نسخ الفاتورة المحددة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                show_error_message("خطأ", error)
                return

            original_invoice = self.session.query(Invoice).get(invoice_id)
            if not original_invoice:
                show_error_message("خطأ", "لم يتم العثور على الفاتورة")
                return

            # إنشاء نسخة من الفاتورة
            dialog = InvoiceDialog(self, session=self.session)

            # ملء البيانات من الفاتورة الأصلية
            dialog.client_combo.setCurrentIndex(dialog.client_combo.findData(original_invoice.client_id))
            dialog.notes_edit.setText(original_invoice.notes or "")

            # نسخ العناصر
            for item in original_invoice.items:
                dialog.items.append({
                    'description': item.description,
                    'quantity': item.quantity,
                    'unit_price': item.unit_price,
                    'total_price': item.total_price
                })

            dialog.update_items_table()
            dialog.update_total()

            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # استخراج عناصر الفاتورة
                    items_data = data.pop('items')

                    # إنشاء فاتورة جديدة في قاعدة البيانات
                    invoice = Invoice(**data)
                    self.session.add(invoice)
                    self.session.flush()  # للحصول على معرف الفاتورة

                    # إضافة عناصر الفاتورة
                    for item_data in items_data:
                        if 'description' in item_data and 'quantity' in item_data and 'unit_price' in item_data and 'total_price' in item_data:
                            item_data['invoice_id'] = invoice.id
                            item = InvoiceItem(**item_data)
                            self.session.add(item)

                    self.session.commit()

                    show_info_message("تم", "تم نسخ الفاتورة بنجاح")
                    self.refresh_data()
        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء نسخ الفاتورة: {str(e)}")

    def export_report_pdf(self):
        """تصدير تقرير الفواتير إلى PDF"""
        try:
            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ تقرير الفواتير", "تقرير_الفواتير.pdf", "ملفات PDF (*.pdf)")
            if not file_path:
                return

            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء مستند نصي
            document = QTextDocument()

            # إنشاء محتوى HTML للتقرير
            html_content = self.generate_invoices_report_html()
            document.setHtml(html_content)

            # طباعة المستند إلى PDF
            document.print_(printer)

            show_info_message("تم", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def generate_invoices_report_html(self):
        """إنشاء محتوى HTML لتقرير الفواتير"""
        try:
            # الحصول على جميع الفواتير
            invoices = self.session.query(Invoice).all()

            # حساب الإحصائيات
            total_amount = sum(invoice.total_amount for invoice in invoices)
            paid_amount = sum(invoice.paid_amount for invoice in invoices)
            remaining_amount = total_amount - paid_amount

            html = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير الفواتير</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #2c3e50; text-align: center; }}
                    h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    th {{ background-color: #3498db; color: white; }}
                    .summary {{ background-color: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                    .total {{ font-weight: bold; color: #2c3e50; }}
                    .paid {{ color: #27ae60; }}
                    .remaining {{ color: #e74c3c; }}
                </style>
            </head>
            <body>
                <h1>📋 تقرير الفواتير</h1>
                <p style="text-align: center;">تاريخ التقرير: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}</p>

                <div class="summary">
                    <h2>📊 ملخص الفواتير</h2>
                    <p class="total">إجمالي عدد الفواتير: {len(invoices)}</p>
                    <p class="total">إجمالي المبالغ: {format_currency(total_amount)}</p>
                    <p class="paid">إجمالي المدفوعات: {format_currency(paid_amount)}</p>
                    <p class="remaining">إجمالي المستحقات: {format_currency(remaining_amount)}</p>
                </div>

                <h2>📋 تفاصيل الفواتير</h2>
                <table>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>التاريخ</th>
                        <th>المبلغ الإجمالي</th>
                        <th>المبلغ المدفوع</th>
                        <th>المبلغ المتبقي</th>
                        <th>الحالة</th>
                    </tr>
            """

            # إضافة صفوف الفواتير
            for invoice in invoices:
                remaining = invoice.total_amount - invoice.paid_amount
                status_map = {
                    'pending': 'قيد الانتظار',
                    'paid': 'مدفوعة',
                    'partially_paid': 'مدفوعة جزئياً',
                    'cancelled': 'ملغاة'
                }
                status_text = status_map.get(invoice.status, invoice.status or "")

                html += f"""
                    <tr>
                        <td>{invoice.invoice_number}</td>
                        <td>{invoice.client.name if invoice.client else ''}</td>
                        <td>{invoice.date.strftime('%Y-%m-%d') if invoice.date else ''}</td>
                        <td>{format_currency(invoice.total_amount)}</td>
                        <td>{format_currency(invoice.paid_amount)}</td>
                        <td>{format_currency(remaining)}</td>
                        <td>{status_text}</td>
                    </tr>
                """

            html += """
                </table>
            </body>
            </html>
            """

            return html
        except Exception as e:
            return f"""
            <html dir="rtl">
            <body>
                <h1>خطأ في إنشاء التقرير</h1>
                <p>حدث خطأ أثناء إنشاء تقرير الفواتير: {str(e)}</p>
            </body>
            </html>
            """

    def show_statistics(self):
        """عرض إحصائيات الفواتير"""
        try:
            invoices = self.session.query(Invoice).all()

            if not invoices:
                show_info_message("إحصائيات الفواتير", "لا توجد فواتير لعرض الإحصائيات")
                return

            # حساب الإحصائيات العامة
            total_invoices = len(invoices)
            total_amount = sum(invoice.total_amount for invoice in invoices)
            total_paid = sum(invoice.paid_amount for invoice in invoices)
            total_remaining = total_amount - total_paid
            avg_invoice = total_amount / total_invoices

            # إحصائيات حسب الحالة
            status_stats = {
                'pending': {'count': 0, 'amount': 0},
                'partially_paid': {'count': 0, 'amount': 0},
                'paid': {'count': 0, 'amount': 0}
            }

            for invoice in invoices:
                status = invoice.status or 'pending'
                if status in status_stats:
                    status_stats[status]['count'] += 1
                    status_stats[status]['amount'] += invoice.total_amount

            # إحصائيات حسب العملاء
            client_stats = {}
            for invoice in invoices:
                if invoice.client:
                    client_name = invoice.client.name
                    if client_name not in client_stats:
                        client_stats[client_name] = {'count': 0, 'amount': 0}
                    client_stats[client_name]['count'] += 1
                    client_stats[client_name]['amount'] += invoice.total_amount

            # إحصائيات شهرية
            from collections import defaultdict
            monthly_stats = defaultdict(lambda: {'count': 0, 'amount': 0})
            for invoice in invoices:
                if invoice.date:
                    month_key = invoice.date.strftime("%Y-%m")
                    monthly_stats[month_key]['count'] += 1
                    monthly_stats[month_key]['amount'] += invoice.total_amount

            # أكبر وأصغر فاتورة
            max_invoice = max(invoices, key=lambda x: x.total_amount)
            min_invoice = min(invoices, key=lambda x: x.total_amount)

            # إنشاء محتوى الإحصائيات
            stats_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                                📊 إحصائيات الفواتير
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ الإحصائيات: {QDate.currentDate().toString('yyyy-MM-dd')}

📈 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
🧾 إجمالي الفواتير: {total_invoices}
💰 إجمالي المبالغ: {int(total_amount):,} جنيه
💳 إجمالي المدفوع: {int(total_paid):,} جنيه
⚠️ إجمالي المتبقي: {int(total_remaining):,} جنيه
📊 متوسط الفاتورة: {int(avg_invoice):,} جنيه
🔺 أكبر فاتورة: {int(max_invoice.total_amount):,} جنيه (فاتورة #{max_invoice.id})
🔻 أصغر فاتورة: {int(min_invoice.total_amount):,} جنيه (فاتورة #{min_invoice.id})

📋 إحصائيات حسب الحالة:
─────────────────────────────────────────────────────────────────────────────
"""

            status_names = {
                'pending': '⏳ معلقة',
                'partially_paid': '🔄 مدفوعة جزئياً',
                'paid': '✅ مدفوعة بالكامل'
            }

            for status, data in status_stats.items():
                if data['count'] > 0:
                    percentage = (data['count'] / total_invoices) * 100
                    stats_content += f"• {status_names.get(status, status)}:\n"
                    stats_content += f"  📝 العدد: {data['count']} فاتورة ({percentage:.1f}%)\n"
                    stats_content += f"  💰 المبلغ: {int(data['amount']):,} جنيه\n\n"

            if client_stats:
                stats_content += """
👥 أفضل العملاء (حسب المبلغ):
─────────────────────────────────────────────────────────────────────────────
"""
                top_clients = sorted(client_stats.items(), key=lambda x: x[1]['amount'], reverse=True)[:5]
                for client_name, data in top_clients:
                    stats_content += f"• {client_name}: {int(data['amount']):,} جنيه ({data['count']} فاتورة)\n"

            if monthly_stats:
                stats_content += """

📅 الإحصائيات الشهرية:
─────────────────────────────────────────────────────────────────────────────
"""
                for month, data in sorted(monthly_stats.items(), reverse=True)[:6]:
                    stats_content += f"• {month}: {int(data['amount']):,} جنيه ({data['count']} فاتورة)\n"

            # عرض الإحصائيات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات الفواتير")
            dialog.setModal(True)
            dialog.resize(700, 600)

            layout = QVBoxLayout()

            text_browser = QTextBrowser()
            text_browser.setPlainText(stats_content)
            text_browser.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 12px;
                    line-height: 1.5;
                    background-color: #e2e8f0;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(text_browser)

            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            layout.addWidget(close_button)

            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def generate_invoices_report(self):
        """إنشاء تقرير الفواتير"""
        try:
            invoices = self.session.query(Invoice).all()

            if not invoices:
                show_info_message("تقرير الفواتير", "لا توجد فواتير لإنشاء التقرير")
                return

            # حساب الإحصائيات
            total_invoices = len(invoices)
            total_amount = sum(invoice.total_amount for invoice in invoices)
            total_paid = sum(invoice.paid_amount for invoice in invoices)
            total_remaining = total_amount - total_paid
            avg_invoice = total_amount / total_invoices

            # إحصائيات حسب الحالة
            status_stats = {
                'pending': {'count': 0, 'amount': 0},
                'partially_paid': {'count': 0, 'amount': 0},
                'paid': {'count': 0, 'amount': 0}
            }

            for invoice in invoices:
                status = invoice.status or 'pending'
                if status in status_stats:
                    status_stats[status]['count'] += 1
                    status_stats[status]['amount'] += invoice.total_amount

            # إنشاء محتوى التقرير
            report_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                                📋 تقرير مفصل للفواتير
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QTime.currentTime().toString('hh:mm:ss')}

📊 ملخص عام:
─────────────────────────────────────────────────────────────────────────────
🧾 إجمالي الفواتير: {total_invoices}
💰 إجمالي المبالغ: {int(total_amount):,} جنيه
💳 إجمالي المدفوع: {int(total_paid):,} جنيه
⚠️ إجمالي المتبقي: {int(total_remaining):,} جنيه
📊 متوسط الفاتورة: {int(avg_invoice):,} جنيه

📈 تفصيل حسب الحالة:
─────────────────────────────────────────────────────────────────────────────
"""

            status_names = {
                'pending': '⏳ معلقة',
                'partially_paid': '🔄 مدفوعة جزئياً',
                'paid': '✅ مدفوعة بالكامل'
            }

            for status, data in status_stats.items():
                if data['count'] > 0:
                    percentage = (data['count'] / total_invoices) * 100
                    report_content += f"• {status_names.get(status, status)}: {data['count']} فاتورة ({percentage:.1f}%) - {int(data['amount']):,} جنيه\n"

            report_content += f"""
─────────────────────────────────────────────────────────────────────────────

📋 تفاصيل الفواتير:
═══════════════════════════════════════════════════════════════════════════════
"""

            for invoice in invoices:
                date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else "غير محدد"
                client_name = invoice.client.name if invoice.client else "غير محدد"
                remaining = invoice.total_amount - invoice.paid_amount
                status_text = status_names.get(invoice.status, invoice.status or 'معلقة')

                report_content += f"""
🔸 فاتورة #{invoice.invoice_number or invoice.id}
   📅 التاريخ: {date_str}
   👤 العميل: {client_name}
   💰 المبلغ الإجمالي: {int(invoice.total_amount):,} جنيه
   💳 المبلغ المدفوع: {int(invoice.paid_amount):,} جنيه
   ⚠️ المبلغ المتبقي: {int(remaining):,} جنيه
   📊 الحالة: {status_text}
   📝 الملاحظات: {invoice.notes or 'لا توجد ملاحظات'}
   ─────────────────────────────────────────────────────────────────────────────
"""

            # عرض التقرير
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("📋 تقرير الفواتير")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout()

            text_browser = QTextBrowser()
            text_browser.setPlainText(report_content)
            text_browser.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    background-color: #e2e8f0;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(text_browser)

            # أزرار الحفظ والطباعة
            buttons_layout = QHBoxLayout()

            save_button = QPushButton("💾 حفظ التقرير")
            save_button.clicked.connect(lambda: self.save_report_to_file(report_content, "تقرير_الفواتير"))
            buttons_layout.addWidget(save_button)

            print_button = QPushButton("🖨️ طباعة")
            print_button.clicked.connect(lambda: self.print_report(text_browser))
            buttons_layout.addWidget(print_button)

            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def save_report_to_file(self, content, filename):
        """حفظ التقرير إلى ملف نصي"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير", f"{filename}.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                show_info_message("تم", f"تم حفظ التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في حفظ التقرير: {str(e)}")

    def print_report(self, text_browser):
        """طباعة التقرير"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                text_browser.print_(printer)
                show_info_message("تم", "تم إرسال التقرير للطباعة بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الطباعة: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            pass

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة بدون مشاكل"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # تخطيط أفقي للإطار مع التوسيط العمودي
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(8, 0, 8, 0)  # إزالة الهوامش العمودية
        filter_layout.setSpacing(8)
        filter_layout.setAlignment(Qt.AlignVCenter)  # توسيط عمودي للعناصر

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 420px;
                min-width: 420px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        self.status_filter_frame.setLayout(filter_layout)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 5px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 500px;
                max-width: 500px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 15px 0px;
                border-radius: 15px;
                margin: 2px;
                min-height: 32px;
                max-height: 32px;
                max-width: 480px;
                min-width: 480px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة العناصر
        filter_options = [
            ("جميع الحالات", None),
            ("قيد الانتظار", "pending"),
            ("مدفوعة", "paid"),
            ("مدفوعة جزئيًا", "partially_paid"),
            ("ملغاة", "cancelled")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص
            action = QAction(f"    {text}    ", self)  # إضافة مسافات للتوسيط
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_invoices()