#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البرنامج بدون رسائل تقنية
يستخدم مرشح متقدم لإخفاء جميع التحذيرات غير المهمة
"""

import sys
import os
import warnings
from contextlib import redirect_stderr, redirect_stdout
from io import StringIO

# إعداد إخفاء التحذيرات قبل استيراد PyQt
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['QT_LOGGING_RULES'] = '*=false'
os.environ['QT_LOGGING_TO_CONSOLE'] = '0'

class AdvancedMessageFilter:
    """مرشح متقدم للرسائل التقنية"""
    
    def __init__(self, original_stream):
        self.original_stream = original_stream
        self.buffer = StringIO()
        
        # قائمة شاملة للرسائل المراد إخفاؤها
        self.blocked_patterns = [
            'Unknown property',
            'text-shadow',
            'box-shadow', 
            'transform',
            'transition',
            'backdrop-filter',
            'does not have a property named',
            'alignment',
            'qproperty-alignment',
            'overflow',
            'text-overflow',
            'letter-spacing',
            'text-transform',
            'QPainter::',
            'QWidgetEffectSourcePrivate::',
            'Painter not active',
            'paint device can only be painted',
            'worldTransform',
            'setWorldTransform',
            'translate',
            'arguments did not match',
            'QWidget::setMinimumSize',
            'QWidget::setMaximumSize',
            'QPixmap::scaled',
            'QFont::',
            'QFontMetrics::',
            'QStyleOption',
            'QStyle::',
            'QPalette::',
            'QBrush::',
            'QColor::',
            'QGradient::'
        ]
        
        # قائمة الرسائل المهمة التي يجب عرضها
        self.important_patterns = [
            'Error',
            'Critical', 
            'Fatal',
            'Exception',
            'خطأ',
            'فشل',
            'تحذير مهم'
        ]

    def write(self, message):
        """كتابة الرسالة مع التصفية"""
        if not message or message.isspace():
            return
            
        # التحقق من الرسائل المهمة أولاً
        if any(pattern in message for pattern in self.important_patterns):
            self.original_stream.write(message)
            return
            
        # إخفاء الرسائل التقنية غير المهمة
        if any(pattern in message for pattern in self.blocked_patterns):
            return
            
        # عرض الرسائل الأخرى
        self.original_stream.write(message)

    def flush(self):
        """تفريغ المخزن المؤقت"""
        self.original_stream.flush()

def setup_silent_mode():
    """إعداد الوضع الصامت للبرنامج"""
    
    # تطبيق مرشح الرسائل على stderr و stdout
    sys.stderr = AdvancedMessageFilter(sys.__stderr__)
    sys.stdout = AdvancedMessageFilter(sys.__stdout__)
    
    # إعداد متغيرات البيئة لإخفاء رسائل Qt
    os.environ.update({
        'QT_LOGGING_RULES': '*.debug=false;*.info=false;*.warning=false',
        'QT_LOGGING_TO_CONSOLE': '0',
        'PYTHONWARNINGS': 'ignore',
        'QT_AUTO_SCREEN_SCALE_FACTOR': '1',
        'QT_ENABLE_HIGHDPI_SCALING': '1'
    })

def main():
    """الدالة الرئيسية للتشغيل الصامت"""
    
    print("🚀 تشغيل البرنامج في الوضع الصامت...")
    print("🔇 تم تفعيل المرشح المتقدم للرسائل التقنية")
    
    # إعداد الوضع الصامت
    setup_silent_mode()
    
    try:
        # استيراد وتشغيل البرنامج الرئيسي
        from main_silent import main as silent_main
        silent_main()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
        sys.exit(0)
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
